import torch
from transformers import AutoModel, AutoTokenizer

model_name = "facebook/esm2_t6_8M_UR50D"
device = "cpu"

model = AutoModel.from_pretrained(model_name).to("cpu")
tokenizer = AutoTokenizer.from_pretrained(model_name)

inputs = tokenizer("MKTLLILAVVAAALA", return_tensors="pt").to(device)

# Get embeddings
with torch.no_grad():
    outputs = model(**inputs)

# Get last hidden state
embeddings = outputs.last_hidden_state

use_pooling = True
# Apply pooling if requested
if use_pooling:
    # Mean pooling (excluding special tokens)
    attention_mask = inputs["attention_mask"]
    embeddings = torch.sum(
        embeddings * attention_mask.unsqueeze(-1), dim=1
    ) / torch.sum(attention_mask, dim=1, keepdim=True)
else:
    # Use CLS token
    embeddings = embeddings[:, 0]

eb = embeddings.cpu().numpy()[0]
pass


def freeze_lower_model_layers(model: nn.Module, highest_frozen_layer: int):
    """
    Freeze lower layers up until highest_frozen_layer. Unfreeze all
    layers above that.
    """
    requires_grad = False
    for name, param in model.named_parameters():
        if not requires_grad:
            p = re.compile("layers\.(\d+)\.")
            matched = p.match(name)
            if matched is not None:
                layer_str = matched.group(1)
                if int(layer_str) == highest_frozen_layer:
                    requires_grad = True
        param.requires_grad = requires_grad
