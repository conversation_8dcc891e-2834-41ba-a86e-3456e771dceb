import torch
from transformers import AutoModel, AutoTokenizer

model_name = "esm2_t6_8M_UR50D"
device = "cpu"

model = AutoModel.from_pretrained(model_name).to("cpu")
tokenizer = AutoTokenizer.from_pretrained(model_name)

inputs = tokenizer([("Protein_X", "MKTLLILAVVAAALA")], return_tensors="pt").to(device)

# Get embeddings
with torch.no_grad():
    outputs = self.model(**inputs)

# Get last hidden state
embeddings = outputs.last_hidden_state

# Apply pooling if requested
if use_pooling:
    # Mean pooling (excluding special tokens)
    attention_mask = inputs["attention_mask"]
    embeddings = torch.sum(
        embeddings * attention_mask.unsqueeze(-1), dim=1
    ) / torch.sum(attention_mask, dim=1, keepdim=True)
else:
    # Use CLS token
    embeddings = embeddings[:, 0]

eb = embeddings.cpu().numpy()[0]
pass


""" model, alphabet = torch.hub.load("facebookresearch/esm:main", "esm2_t33_650M_UR50D")

batch_converter = alphabet.get_batch_converter()

# Define the sequence
sequence = [("Protein_X", "MKTLLILAVVAAALA")]

# Convert to model input format
batch_labels, batch_strs, batch_tokens = batch_converter(sequence)

# Perform predictions
predictions = model(batch_tokens)
print(predictions)
"""
