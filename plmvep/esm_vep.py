import re
import pandas as pd
import torch
from torch import nn
from torch.utils.data import DataLoader
from sklearn.model_selection import KFold
import torch.optim as optim


# from esm.model.esm2 import ESM2

device = torch.accelerator.current_accelerator().type \
          if torch.accelerator.is_available() else "cpu"


def load_pretrained_esm_model(model_name: str,
                              device: str = "cpu") -> nn.Module:
    repr_layer = int(model_name.split('_')[1][1:])
    model, alphabet = torch.hub.load("facebookresearch/esm:main",
                                     model_name)
    batch_converter = alphabet.get_batch_converter()
    return model.to(device), alphabet, batch_converter, repr_layer


def freeze_lower_model_layers(model: nn.Module, highest_frozen_layer: int):
    """
    Freeze lower layers up until highest_frozen_layer. Unfreeze all
    layers above that.
    """
    requires_grad = False
    for name, param in model.named_parameters():
        if not requires_grad:
            p = re.compile("layers\.(\d+)\.")
            matched = p.match(name)
            if matched is not None:
                layer_str = matched.group(1)
                if int(layer_str) == highest_frozen_layer:
                    requires_grad = True
        param.requires_grad = requires_grad


def train_one_epoch(epoch_index, model, optimizer, criterion,
                    training_loader, tb_writer):
    model.train(True)
    running_loss = 0.
    last_loss = 0.
    epoch_loss = 0

    # Here, we use enumerate(training_loader) instead of
    # iter(training_loader) so that we can track the batch
    # index and do some intra-epoch reporting
    for i, data in enumerate(training_loader):
        # Every data instance is an input + label pair
        sequences, labels = data

        # Prepare input
        _, _, batch_tokens = batch_converter(sequences)
        batch_tokens = batch_tokens.to(device)
        labels = labels.to(device)

        # Zero your gradients for every batch!
        optimizer.zero_grad()

        # Make predictions for this batch
        outputs = model(batch_tokens)["logits"]

        # Compute the loss and its gradients
        loss = criterion(outputs, labels)
        loss.backward()

        # Adjust learning weights
        optimizer.step()

        # Gather data and report
        running_loss += loss.item()
        epoch_loss += loss.item()
        if i % 1000 == 999:
            last_loss = running_loss / 1000  # loss per batch
            print('  batch {} loss: {}'.format(i + 1, last_loss))
            tb_x = epoch_index * len(training_loader) + i + 1
            tb_writer.add_scalar('Loss/train', last_loss, tb_x)
            running_loss = 0.

    avg_per_batch_loss = epoch_loss/len(training_loader)
    print(f"Epoch {epoch_index+1}, Loss: {avg_per_batch_loss}")
    return avg_per_batch_loss  # last_loss


def reset_weights(model):
    '''
    Try resetting model weights to avoid
    weight leakage.
    '''
    for layer in model.children():
        if hasattr(layer, 'reset_parameters'):
            print(f'Reset trainable parameters of layer = {layer}')
            layer.reset_parameters()


k_folds = 5
num_epochs = 1
loss_function = nn.CrossEntropyLoss()
  
# For fold results
  
# Set fixed random number seed
torch.manual_seed(42)

"""
Protein_ID,Sequence,Label
P001,MKTLLILAVVAAALA,Helix
P002,MGAVVLAIVAAALVG,Sheet
P003,MHTLLILAIVAAFLV,Loop

"""

data = pd.DataFrame({"Sequence": ["MKTLLILAVVAAALA","MGAVVLAIVAAALVG",
	"MHTLLILAIVAAFLV"], "Label": ["Helix","Sheet","Loop"]})

# Split dataset
# train_data, test_data = train_test_split(data, test_size=0.1, random_state=42)
# train_data, val_data = train_test_split(train_data, test_size=0.2, random_state=42)

# model, alphabet = pretrained.esm3()
# batch_converter = alphabet.get_batch_converter()

import torch
from torch.utils.data import DataLoader, Dataset


class ProteinDataset(Dataset):
    def __init__(self, sequences, labels):
        self.sequences = sequences
        self.labels = labels

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        return self.sequences[idx], self.labels[idx]

# Load data
data_sequences = data["Sequence"].tolist()
data_labels = data["Label"].tolist()
dataset = ProteinDataset(data_sequences, data_labels)
data_loader = DataLoader(dataset, batch_size=16, shuffle=True)

# Define loss function and optimizer
loss_function = nn.CrossEntropyLoss()
# optimizer = optim.Adam(model.parameters(), lr=1e-4)
k_folds = 3

# Define the K-fold Cross Validator
kfold = KFold(n_splits=k_folds, shuffle=True)

learning_rate = 1e-3

optimizer_factory_func = lambda model: torch.optim.Adam(
    filter(lambda p: p.requires_grad, model.parameters()),
    lr=learning_rate)

def make_predictions(model, inputs, device):
    _, _, batch_tokens = batch_converter(inputs)
    batch_tokens = batch_tokens.to(device)
    labels = labels.to(device)

    # Make predictions for this batch
    return model(batch_tokens)["logits"]
        
    
# Start print
print('--------------------------------')

def k_fold_cross_validation(num_epochs, dataset,
                            model_factory_func,
                            optimizer_factory_func,
                            loss_function,
                            make_predictions_func,
                            batch_print_interval, device):
    # K-fold Cross Validation model evaluation
    results = {}
    for fold, (train_ids, test_ids) in enumerate(kfold.split(dataset)):
        
        # Print
        print(f'FOLD {fold}')
        print('--------------------------------')
        
        # Sample elements randomly from a given list of ids, no replacement.
        train_subsampler = torch.utils.data.SubsetRandomSampler(train_ids)
        test_subsampler = torch.utils.data.SubsetRandomSampler(test_ids)
        
        # Define data loaders for training and testing data in this fold
        trainloader = torch.utils.data.DataLoader(
                        dataset, 
                        batch_size=10, sampler=train_subsampler)
        testloader = torch.utils.data.DataLoader(
                        dataset,
                        batch_size=10, sampler=test_subsampler)
        
        # Init the neural network
        model = model_factory_func()
        
        # Initialize optimizer
        optimizer = optimizer_factory_func(model)
        
        # Run the training loop for defined number of epochs
        for epoch in range(0, num_epochs):

            # Print epoch
            print(f'Starting epoch {epoch+1}')

            # Set current loss value
            current_loss = 0.0

            # Iterate over the DataLoader for training data
            for i, data in enumerate(trainloader, 0):
                
                # Get inputs
                inputs, targets = data
                
                # Zero the gradients
                optimizer.zero_grad()

                # Perform forward pass
                outputs = make_predictions_func(model, inputs, device)
                
                # Compute loss
                loss = loss_function(outputs, targets)
                
                # Perform backward pass
                loss.backward()
                
                # Perform optimization
                optimizer.step()
                
                # Print statistics
                current_loss += loss.item()
                if i % batch_print_interval == batch_print_interval -1 :
                    print('Loss after mini-batch %5d: %.3f' %
                        (i + 1, current_loss / batch_print_interval))
                    current_loss = 0.0
                    
            # Process is complete.
            print('Training process has finished. Saving trained model.')

            # Print about testing
            print('Starting testing')
            
            # Saving the model
            save_path = f'./model-fold-{fold}.pth'
            torch.save(model.state_dict(), save_path)

            # Evaluationfor this fold
            correct, total = 0, 0
            with torch.no_grad():

            # Iterate over the test data and generate predictions
            for i, data in enumerate(testloader, 0):

                # Get inputs
                inputs, targets = data

                # Generate outputs
                outputs = make_predictions_func(model, inputs, device)

                # Set total and correct
                _, predicted = torch.max(outputs.data, 1)
                total += targets.size(0)
                correct += (predicted == targets).sum().item()

            # Print accuracy
            print('Accuracy for fold %d: %d %%' % (fold, 100.0 * correct / total))
            print('--------------------------------')
            results[fold] = 100.0 * (correct / total)
            
    # Print fold results
    print(f'K-FOLD CROSS VALIDATION RESULTS FOR {k_folds} FOLDS')
    print('--------------------------------')
    sum = 0.0
    for key, value in results.items():
        print(f'Fold {key}: {value} %')
        sum += value
    print(f'Average: {sum/len(results.items())} %')


model, alphabet, batch_converter, repr_layer = (
    load_pretrained_esm_model(
        #   model_name='esm1b_t33_650M_UR50S') #,device='cuda')
        model_name='esm2_t30_150M_UR50D'))  # ,device='cuda')
freeze_lower_model_layers(model, 20)
for name, param in model.named_parameters():
    print(name, param.requires_grad)
pass