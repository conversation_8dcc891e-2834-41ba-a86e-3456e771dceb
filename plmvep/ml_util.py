import torch


def train_one_epoch(
    epoch_index, model, optimizer, criterion, training_loader, tb_writer
):
    model.train(True)
    running_loss = 0.0
    last_loss = 0.0
    epoch_loss = 0

    # Here, we use enumerate(training_loader) instead of
    # iter(training_loader) so that we can track the batch
    # index and do some intra-epoch reporting
    for i, data in enumerate(training_loader):
        # Every data instance is an input + label pair
        sequences, labels = data

        # Prepare input
        _, _, batch_tokens = batch_converter(sequences)
        batch_tokens = batch_tokens.to(device)
        labels = labels.to(device)

        # Zero your gradients for every batch!
        optimizer.zero_grad()

        # Make predictions for this batch
        outputs = model(batch_tokens)["logits"]

        # Compute the loss and its gradients
        loss = criterion(outputs, labels)
        loss.backward()

        # Adjust learning weights
        optimizer.step()

        # Gather data and report
        running_loss += loss.item()
        epoch_loss += loss.item()
        if i % 1000 == 999:
            last_loss = running_loss / 1000  # loss per batch
            print("  batch {} loss: {}".format(i + 1, last_loss))
            tb_x = epoch_index * len(training_loader) + i + 1
            tb_writer.add_scalar("Loss/train", last_loss, tb_x)
            running_loss = 0.0

    avg_per_batch_loss = epoch_loss / len(training_loader)
    print(f"Epoch {epoch_index+1}, Loss: {avg_per_batch_loss}")
    return avg_per_batch_loss  # last_loss


def train_one_epoch(
    fold,
    epoch_index,
    model,
    make_predictions_func,
    optimizer,
    loss_function,
    training_loader,
    device,
    batch_print_interval,
    tb_writer,
):

    # Print epoch
    print(f"Starting epoch {epoch_index+1}")
    starting_mini_batch_idx = epoch_index * len(training_loader)
    epoch_loss = 0.0

    # Set current loss value
    current_loss = 0.0

    # Iterate over the DataLoader for training data
    for i, data in enumerate(training_loader, 0):

        # Get inputs
        inputs, targets = data

        # Zero the gradients
        optimizer.zero_grad()

        # Perform forward pass
        outputs = make_predictions_func(model, inputs, device)

        # Compute loss
        loss = loss_function(outputs, targets)

        # Perform backward pass
        loss.backward()

        # Perform optimization
        optimizer.step()

        # Print statistics after every batch_print_interval batches.
        # Average loss per batch for the last batch_print_interval batches.
        current_loss += loss.item()
        epoch_loss += loss.item()
        if i % batch_print_interval == batch_print_interval - 1:
            total_min_batch_idx = starting_mini_batch_idx + i + 1
            epoch_mini_batch_idx = i + 1
            tb_writer.add_scalar(
                f"Avg batch loss/{batch_print_interval} batches fold{fold}",
                current_loss / batch_print_interval,
                total_min_batch_idx,
            )
            current_loss = 0.0

    avg_per_batch_loss = epoch_loss / len(training_loader)
    print(f"Avg loss per batch for Epoch {epoch_index+1}: {avg_per_batch_loss:.3f}")
    tb_writer.add_scalar(
        f"Avg batch loss/epoch fold{fold}", avg_per_batch_loss, epoch_index + 1
    )
    return avg_per_batch_loss  # last_loss


def reset_weights(model):
    """
    Try resetting model weights to avoid
    weight leakage.
    """
    for layer in model.children():
        if hasattr(layer, "reset_parameters"):
            print(f"Reset trainable parameters of layer = {layer}")
            layer.reset_parameters()


def k_fold_cross_validation(
    num_epochs,
    dataset,
    kfold_func,
    model_factory_func,
    optimizer_factory_func,
    loss_function,
    make_predictions_func,
    validation_funcs,
    batch_print_interval,
    tb_writer,
    device,
):
    # K-fold Cross Validation model evaluation
    results = {}
    for fold, (train_ids, test_ids) in enumerate(kfold_func.split(dataset)):

        # Print
        print(f"FOLD {fold}")
        print("--------------------------------")

        # Sample elements randomly from a given list of ids, no replacement.
        train_subsampler = torch.utils.data.SubsetRandomSampler(train_ids)
        test_subsampler = torch.utils.data.SubsetRandomSampler(test_ids)

        # Define data loaders for training and testing data in this fold
        training_loader = torch.utils.data.DataLoader(
            dataset, batch_size=10, sampler=train_subsampler
        )
        testloader = torch.utils.data.DataLoader(
            dataset, batch_size=10, sampler=test_subsampler
        )

        # Init the neural network
        model = model_factory_func()

        # Initialize optimizer
        optimizer = optimizer_factory_func(model)

        # Run the training loop for defined number of epochs
        for epoch in range(0, num_epochs):

            # Print epoch
            print(f"Starting epoch {epoch+1}")

            train_one_epoch(
                fold,
                epoch,
                model,
                make_predictions_func,
                optimizer,
                loss_function,
                training_loader,
                device,
                batch_print_interval,
                tb_writer,
            )

        # Process is complete.
        print("Training process has finished. Saving trained model.")

        # Print about testing
        print("Starting testing")

        # Saving the model
        save_path = f"./model-fold-{fold}.pth"
        torch.save(model.state_dict(), save_path)

        # Evaluationfor this fold
        with torch.no_grad():

            targets_list = []
            outputs_list = []
            # Iterate over the test data and generate predictions
            for i, data in enumerate(testloader, 0):

                # Get inputs
                inputs, targets = data
                targets_list.append(targets)

                # Generate outputs
                outputs = make_predictions_func(model, inputs, device)
                outputs_list.append(outputs)

            targets = torch.cat(targets_list, dim=0)
            outputs = torch.cat(outputs_list, dim=0)

        for validation_func in validation_funcs:
            validation_result = validation_func["func"](targets, outputs)
            validation_results.append[{}]

        # Print accuracy
        print("Accuracy for fold %d: %d %%" % (fold, 100.0 * correct / total))
        print("--------------------------------")
        results[fold] = 100.0 * (correct / total)

    # Print fold results
    print(f"K-FOLD CROSS VALIDATION RESULTS FOR {k_folds} FOLDS")
    print("--------------------------------")
    sum = 0.0
    for key, value in results.items():
        print(f"Fold {key}: {value} %")
        sum += value
    print(f"Average: {sum/len(results.items())} %")
