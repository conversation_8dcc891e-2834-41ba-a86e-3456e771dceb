from torch import nn


class BinaryClassificationModel(nn.Module):
    def __init__(self, esm_model, num_classes):
        super(BinaryClassificationModel, self).__init__()
        self.esm = esm_model
        self.fc = nn.Linear(768, num_classes)

    def forward(self, tokens):
        outputs = self.esm(tokens)
        cls_embedding = outputs["representations"][0][:, 0, :]  # CLS token
        return self.fc(cls_embedding)
