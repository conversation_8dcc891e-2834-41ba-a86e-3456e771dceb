import torch

model, alphabet = torch.hub.load("facebookresearch/esm:main", "esm2_t6_8M_UR50D")

batch_converter = alphabet.get_batch_converter()

# Define the sequence
sequence = [("Protein_X", "MKTLLILAVVAAALA")]

# Convert to model input format
batch_labels, batch_strs, batch_tokens = batch_converter(sequence)

# Perform predictions
with torch.no_grad():
    # For ESM2_t6_8M, we need to specify repr_layers to get representations
    # The model has 6 layers (0-5), let's get the last layer (5)
    # Get representations from all layers to see what's available
    predictions = model(batch_tokens, repr_layers=list(range(6)), return_contacts=False)

# Debug: Check what's available
print("Available keys in predictions:", predictions.keys())
if "representations" in predictions:
    available_layers = list(predictions["representations"].keys())
    print("Available representation layers:", available_layers)

    # Show details about each layer
    for layer in available_layers:
        layer_repr = predictions["representations"][layer]
        print(f"Layer {layer}: shape = {layer_repr.shape}")

    # Check if layer 6 exists
    if 6 in predictions["representations"]:
        print("Layer 6 contents:")
        layer_6_repr = predictions["representations"][6]
        print(f"  Shape: {layer_6_repr.shape}")
        print(f"  First few values: {layer_6_repr[0, 0, :5]}")
    else:
        print("Layer 6 does NOT exist in this model!")
        print(f"Available layers are: {available_layers}")
        print("ESM2_t6_8M has 6 layers numbered 0-5, so layer 6 is out of range")

    if predictions["representations"]:
        print("Representations dictionary has content")
    else:
        print("Representations dictionary is empty!")
else:
    print("No representations found in predictions")

# Extract embeddings
if "representations" in predictions and predictions["representations"]:
    # Method 1: Get embeddings from the specified layer
    embeddings = predictions["representations"][5]
    print(f"Successfully got embeddings with shape: {embeddings.shape}")
else:
    # Fallback: try to get embeddings differently
    print("Trying alternative method to get embeddings...")
    # Re-run with all layers
    predictions = model(batch_tokens, repr_layers=list(range(6)))
    print("Now available layers:", list(predictions["representations"].keys()))
    embeddings = predictions["representations"][5]

# Method 2: Get per-token embeddings (excluding special tokens)
# Remove CLS and SEP tokens (first and last positions)
sequence_embeddings = embeddings[0, 1:-1]  # Shape: [seq_len-2, hidden_dim]

# Method 3: Get sequence-level embedding using mean pooling
sequence_mean_embedding = sequence_embeddings.mean(dim=0)  # [hidden_dim]

# Method 4: Get CLS token embedding (often used for sequence classification)
cls_embedding = embeddings[0, 0]  # Shape: [hidden_dim]

print(f"Full embeddings shape: {embeddings.shape}")
print(f"Sequence embeddings shape (no special tokens): " f"{sequence_embeddings.shape}")
print(f"Mean pooled embedding shape: {sequence_mean_embedding.shape}")
print(f"CLS embedding shape: {cls_embedding.shape}")

# Convert to numpy if needed
embeddings_numpy = sequence_mean_embedding.cpu().numpy()
print(f"Numpy embedding shape: {embeddings_numpy.shape}")
