import torch

model, alphabet = torch.hub.load("facebookresearch/esm:main", "esm2_t6_8M_UR50D")

batch_converter = alphabet.get_batch_converter()

# Define the sequence
sequence = [("Protein_X", "MKTLLILAVVAAALA")]

# Convert to model input format
batch_labels, batch_strs, batch_tokens = batch_converter(sequence)

# Perform predictions
with torch.no_grad():
    predictions = model(
        batch_tokens, repr_layers=[6]
    )  # Get representations from layer 6

# Extract embeddings
# Method 1: Get embeddings from the last layer (layer 6 for this model)
embeddings = predictions["representations"][
    6
]  # Shape: [batch_size, seq_len, hidden_dim]

# Method 2: Get per-token embeddings (excluding special tokens)
# Remove CLS and SEP tokens (first and last positions)
sequence_embeddings = embeddings[0, 1:-1]  # Shape: [seq_len-2, hidden_dim]

# Method 3: Get sequence-level embedding using mean pooling
sequence_mean_embedding = sequence_embeddings.mean(dim=0)  # Shape: [hidden_dim]

# Method 4: Get CLS token embedding (often used for sequence classification)
cls_embedding = embeddings[0, 0]  # Shape: [hidden_dim]

print(f"Full embeddings shape: {embeddings.shape}")
print(f"Sequence embeddings shape (no special tokens): {sequence_embeddings.shape}")
print(f"Mean pooled embedding shape: {sequence_mean_embedding.shape}")
print(f"CLS embedding shape: {cls_embedding.shape}")

# Convert to numpy if needed
embeddings_numpy = sequence_mean_embedding.cpu().numpy()
print(f"Numpy embedding shape: {embeddings_numpy.shape}")
